name: Build and Release

on:
  push:
    branches: [ main, master ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main, master ]

jobs:
  build:
    runs-on: ${{ matrix.os }}
    
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build:all
        
    - name: Upload artifacts (Ubuntu)
      if: matrix.os == 'ubuntu-latest'
      uses: actions/upload-artifact@v4
      with:
        name: linux-builds
        path: |
          dist/*.AppImage
          dist/*.deb
          dist/*.rpm
          dist/latest-linux.yml
          
    - name: Upload artifacts (Windows)
      if: matrix.os == 'windows-latest'
      uses: actions/upload-artifact@v4
      with:
        name: windows-builds
        path: |
          dist/*.exe
          dist/*.exe.blockmap
          dist/latest.yml
          
    - name: Upload artifacts (macOS)
      if: matrix.os == 'macos-latest'
      uses: actions/upload-artifact@v4
      with:
        name: macos-builds
        path: |
          dist/*.dmg
          dist/*.dmg.blockmap
          dist/*.zip
          dist/*.zip.blockmap
          dist/latest-mac.yml

  release:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download all artifacts
      uses: actions/download-artifact@v4
      
    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        files: |
          linux-builds/*
          windows-builds/*
          macos-builds/*
        draft: false
        prerelease: false
        generate_release_notes: true
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
